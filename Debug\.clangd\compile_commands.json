[{"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/Ganway.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/Ganway_Optimized.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/No_Mcu_Ganv_Grayscale_Sensor.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/OLED/oled.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/Pivot_Turn_Test.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/Scheduler.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/Square_Track_Example.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/Track_Example.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/encoder.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/key.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/motor.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/app/ringbuffer.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/bsp/bsp_usart.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/bsp/systick.c"}, {"directory": "C:/Users/<USER>/Desktop/wenjian/10086/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/wenjian/10086/bsp\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/app/OLED\" -I\"C:/Users/<USER>/Desktop/wenjian/10086\" -I\"C:/Users/<USER>/Desktop/wenjian/10086/Debug\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_01_00/source\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/bsp\" -I\"C:/Users/<USER>/workspace_ccstheia/test1/app\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/wenjian/10086/empty.c"}]