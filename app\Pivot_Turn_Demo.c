/**
 * @file Pivot_Turn_Demo.c
 * @brief 原地转向演示函数实现
 * @details 提供简单的原地转向测试函数
 */

#include "Pivot_Turn_Demo.h"
#include "bsp_system.h"

/**
 * @brief 简单的原地转向演示
 */
void Simple_Pivot_Demo(void)
{
    // 演示右转原地转向
    Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);
    delay_ms(300);  // 转300ms
    Motor_Stop();
    delay_ms(500);  // 停止500ms
    
    // 演示左转原地转向
    Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED);
    delay_ms(300);  // 转300ms
    Motor_Stop();
    delay_ms(500);  // 停止500ms
}

/**
 * @brief 原地转向角度测试
 */
void Pivot_Turn_Angle(int direction, int duration_ms)
{
    if(direction != 0) {
        Motor_Square_Pivot_Turn(direction, TRACK_SQUARE_PIVOT_SPEED);
        delay_ms(duration_ms);
        Motor_Stop();
    } else {
        Motor_Stop();
    }
}

/**
 * @brief 检测并执行原地转向
 */
bool Execute_Pivot_If_Needed(unsigned char digital_data, int error)
{
    // 使用现有的检测函数
    bool need_pivot = Detect_Sharp_Corner(digital_data, error);
    
    if(need_pivot) {
        if(error > 0) {
            // 右转原地转向
            Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);
        } else {
            // 左转原地转向
            Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED);
        }
        
        // 短暂的原地转向时间
        delay_ms(150);  // 可根据实际效果调整
        
        return true;
    }
    
    return false;
}
