#ifndef _GANWAY_OPTIMIZED_H
#define _GANWAY_OPTIMIZED_H

#include "motor.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h"
#include "Track_Config.h"  // 引入配置文件
#include <stdbool.h>       // 引入bool类型支持

// 循迹算法配置参数（从配置文件获取）
#define SENSOR_COUNT        TRACK_SENSOR_COUNT
#define CENTER_POSITION     TRACK_CENTER_POSITION
#define WEIGHT_FACTOR       TRACK_WEIGHT_FACTOR

// 循迹模式枚举
typedef enum {
    TRACK_MODE_BASIC = 0,      // 基础模式（原有逻辑）
    TRACK_MODE_WEIGHTED,       // 加权位置模式
    TRACK_MODE_PID,            // PID控制模式
    TRACK_MODE_ADAPTIVE        // 自适应模式
} Track_Mode_t;

// 循迹状态枚举
typedef enum {
    TRACK_STATE_NORMAL = 0,    // 正常循迹
    TRACK_STATE_LOST,          // 丢线状态
    TRACK_STATE_INTERSECTION,  // 十字路口
    TRACK_STATE_TURN_LEFT,     // 左转
    TRACK_STATE_TURN_RIGHT,    // 右转
    TRACK_STATE_STOP           // 停止
} Track_State_t;

// 循迹控制参数结构体
typedef struct {
    Track_Mode_t mode;         // 当前模式
    Track_State_t state;       // 当前状态
    int base_speed;            // 基础速度
    float line_position;       // 黑线位置
    int error;                 // 位置误差
    int last_error;            // 上次误差
    int lost_count;            // 丢线计数
    int intersection_count;    // 路口计数
    unsigned char last_digital; // 上次数字状态
} Track_Control_t;

// 全局循迹控制变量
extern Track_Control_t track_ctrl;

// 函数声明
void Track_Init(void);
void Track_Set_Mode(Track_Mode_t mode);
void Track_Set_Speed(int speed);

// 核心循迹函数
void Way_Optimized(unsigned char digital_data, unsigned short *analog_data);
float Calculate_Line_Position(unsigned char digital_data, unsigned short *analog_data);
int Calculate_Position_Error(float line_position);
Track_State_t Analyze_Track_State(unsigned char digital_data);

// 不同模式的控制函数
void Track_Basic_Control(unsigned char digital_data);
void Track_Weighted_Control(unsigned char digital_data, unsigned short *analog_data);
void Track_PID_Control(unsigned char digital_data, unsigned short *analog_data);
void Track_Adaptive_Control(unsigned char digital_data, unsigned short *analog_data);
void Track_Square_Corner_Control(unsigned char digital_data, unsigned short *analog_data);  // 正方形轨道专用

// 直角转弯检测函数
bool Detect_Sharp_Corner(unsigned char digital_data, int error);

// 特殊状态处理函数
void Handle_Lost_Line(void);
void Handle_Intersection(void);
void Handle_Sharp_Turn(int direction);

// 调试和监控函数
void Track_Debug_Info(void);
void Track_Calibrate_Sensors(No_MCU_Sensor *sensor);

// 状态获取和参数设置函数
Track_Control_t* Track_Get_Status(void);
void Track_Set_PID_Params(float kp, float ki, float kd);
void Track_Reset_PID_Integral(void);
void Track_Emergency_Stop(void);

#endif
