# 正方形轨道循迹优化配置说明

## 🎯 问题解决方案

### 原问题分析
- **速度过快**：原基础速度4000对正方形轨道的直角转弯过快
- **转弯半径过大**：转弯时无法准确跟随轨迹，容易冲出预定路径
- **直角转弯困难**：现有控制策略不适合90度直角转弯

### 优化方案概述
1. **降低基础速度**：从4000降至3200
2. **原地转向策略**：直角转弯时一轮停止(0)，另一轮转动(2800)
3. **智能转弯检测**：自动识别直角转弯并切换到原地转向模式
4. **分级控制策略**：小偏差微调，中偏差差速，大偏差原地转向
5. **自适应速度调整**：根据轨道状态动态调整速度

## ⚙️ 关键配置参数

### 速度配置
```c
#define TRACK_BASE_SPEED_NORMAL     3200    // 正常模式基础速度（优化后）
#define TRACK_SQUARE_STRAIGHT_SPEED 3200    // 正方形轨道直线速度
#define TRACK_SQUARE_CORNER_SPEED   2400    // 正方形轨道转弯速度
```

### 转向控制配置
```c
#define TRACK_TURN_DIFF             1000    // 标准转向差速（减小）
#define TRACK_SQUARE_TURN_DIFF      1200    // 正方形轨道转弯差速
#define TRACK_SQUARE_CORNER_RATIO   0.75f   // 直角转弯时速度比例
#define TRACK_SMOOTH_FACTOR         6       // 平滑控制系数（提高响应）

// 原地转向专用配置
#define TRACK_SQUARE_PIVOT_SPEED    2800    // 直角转弯时转动轮的速度
#define TRACK_SQUARE_STOP_SPEED     0       // 直角转弯时停止轮的速度
#define TRACK_SQUARE_PIVOT_THRESHOLD 3      // 启用原地转向的误差阈值
```

## 🚀 使用方法

### 方法1：快速应用（推荐）
在`empty.c`主程序中已经更新为使用优化算法：
```c
// 使用专门优化的算法（针对正方形轨道直角转弯问题）
Way_Optimized(Digtal, Analog_Values);
```

### 方法2：使用专用示例
参考`app/Square_Track_Example.c`中的示例：
```c
// 基础使用
Square_Track_Basic_Example(sensor);

// 高级配置（动态速度调整）
Square_Track_Advanced_Example(sensor);

// 调试模式
Square_Track_Debug_Example(sensor);
```

### 方法3：手动配置
```c
// 1. 初始化
Track_Init();

// 2. 设置自适应模式
Track_Set_Mode(TRACK_MODE_ADAPTIVE);

// 3. 设置适合正方形轨道的速度
Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);

// 4. 在主循环中使用
Way_Optimized(digital_data, analog_data);
```

## 🔧 参数调优指南

### 如果转弯仍然过快
1. 进一步降低转弯速度：
   ```c
   #define TRACK_SQUARE_CORNER_SPEED   2000    // 从2400降至2000
   ```

2. 增大转弯差速：
   ```c
   #define TRACK_SQUARE_TURN_DIFF      1400    // 从1200增至1400
   ```

### 如果转弯反应不够灵敏
1. 减小平滑控制系数：
   ```c
   #define TRACK_SMOOTH_FACTOR         4       // 从6减至4
   ```

2. 调整转弯检测阈值：
   ```c
   // 在Motor_Square_Corner_Control函数中
   if(abs(error) > 1) {  // 从2改为1，更早检测转弯
   ```

### 如果直线行驶过慢
1. 适当提高直线速度：
   ```c
   #define TRACK_SQUARE_STRAIGHT_SPEED 3400    // 从3200增至3400
   ```

## 📊 性能对比

| 参数 | 优化前 | 优化后 | 改善效果 |
|------|--------|--------|----------|
| 基础速度 | 4000 | 3200 | 降低20% |
| 转弯速度 | 4000 | 2400 | 降低40% |
| 转弯差速 | 1200 | 1000-1400 | 更精细控制 |
| 控制响应 | 8 | 6 | 提高33% |

## 🎛️ 高级功能

### 自适应速度调整
系统会根据检测到的轨道状态自动调整控制策略：
- **直线状态**：使用正常速度(3200)，两轮同速
- **轻微偏差**：使用差速控制，快轮3200，慢轮2000-2400
- **中等偏差**：使用大差速控制，快轮3200，慢轮800-1600
- **直角转弯**：使用原地转向，转动轮2800，停止轮0
- **丢线状态**：使用搜索模式，基于历史误差方向搜索

### 原地转向策略详解

#### 🎯 核心原理
正方形轨道的直角转弯需要最小转弯半径，传统的差速转弯会导致转弯半径过大。原地转向策略通过让一个轮子完全停止，另一个轮子转动来实现最小转弯半径。

#### 🔧 控制逻辑
```c
// 检测到直角转弯时：
if(error >= 3) {  // 误差阈值
    if(error > 0) {
        // 右转：左轮转动2800，右轮停止0
        Set_PWM(2800, 0);
    } else {
        // 左转：左轮停止0，右轮转动2800
        Set_PWM(0, 2800);
    }
}
```

#### 📊 转弯效果对比
| 策略 | 左轮速度 | 右轮速度 | 转弯半径 | 适用场景 |
|------|----------|----------|----------|----------|
| 传统差速 | 3200 | 1600 | 大 | 缓弯 |
| 大差速 | 3200 | 800 | 中 | 急弯 |
| **原地转向** | **2800** | **0** | **最小** | **直角** |

### 转弯检测优化
- **连续检测**：连续2次检测到转弯才确认是直角
- **误差阈值**：误差大于等于3时启用原地转向模式
- **传感器模式**：边缘传感器检测到黑线且中心传感器丢线
- **历史记忆**：基于上次误差方向进行丢线搜索

## 🔍 调试功能

### 启用调试模式
```c
#define TRACK_DEBUG_ENABLE          1       // 启用调试功能
```

### 查看实时状态
```c
Track_Control_t *status = Track_Get_Status();
// 可以查看：
// status->base_speed    // 当前速度
// status->error         // 当前误差
// status->state         // 当前状态
```

## ⚠️ 注意事项

1. **编译后测试**：修改配置后需要重新编译并测试
2. **逐步调整**：建议先使用默认优化参数，再根据实际效果微调
3. **安全第一**：测试时确保小车周围有足够的安全空间
4. **备份原配置**：修改前备份原有的配置参数

## 🔧 编译和部署

### 编译步骤
1. **使用Code Composer Studio (CCS)**：
   - 打开CCS IDE
   - 导入或打开项目
   - 点击"Build"按钮重新编译
   - 确保没有编译错误

2. **如果遇到编译错误**：
   - 检查所有头文件是否正确包含
   - 确认`stdbool.h`、`stdlib.h`、`string.h`已包含
   - 验证所有函数声明和定义匹配

3. **烧录程序**：
   - 连接调试器和目标板
   - 在CCS中点击"Debug"或"Flash"
   - 等待烧录完成

## 🚨 故障排除

### 问题：小车仍然冲出轨道
**解决方案**：
1. 进一步降低`TRACK_SQUARE_CORNER_SPEED`至2000或更低
2. 增大`TRACK_SQUARE_TURN_DIFF`至1500
3. 检查传感器是否正常工作

### 问题：转弯过慢或卡顿
**解决方案**：
1. 适当提高`TRACK_SQUARE_CORNER_SPEED`至2600
2. 减小`TRACK_SMOOTH_FACTOR`至4
3. 检查电机是否有机械阻力

### 问题：直线行驶不稳定
**解决方案**：
1. 检查`TRACK_SQUARE_STRAIGHT_SPEED`是否过高
2. 调整PID参数：`TRACK_PID_KP`、`TRACK_PID_KI`、`TRACK_PID_KD`
3. 确认传感器校准是否正确

## 🧪 原地转向测试指南

### 基础功能测试
使用`app/Pivot_Turn_Test.c`中的测试函数：

```c
// 1. 基础原地转向测试
Pivot_Turn_Basic_Test();        // 测试左右转向基本功能

// 2. 角度精度测试
Pivot_Turn_Angle_Test();        // 测试90度、180度转向

// 3. 速度对比测试
Pivot_Turn_Speed_Test();        // 测试不同速度的转向效果

// 4. 传感器触发测试
Pivot_Turn_Sensor_Test(sensor); // 基于传感器的实际转向测试
```

### 参数调优测试步骤

#### 步骤1：确定最佳转向速度
1. 从2000开始测试，逐步增加到3200
2. 观察转向的平滑性和响应速度
3. 推荐范围：2400-2800

#### 步骤2：调整误差阈值
```c
#define TRACK_SQUARE_PIVOT_THRESHOLD 3  // 从3开始测试
```
- 阈值太小(1-2)：容易误触发原地转向
- 阈值太大(4-5)：可能错过直角转弯
- 推荐值：3

#### 步骤3：优化转向持续时间
在实际循迹中，原地转向的持续时间很重要：
- 太短：转向不足，仍会冲出轨道
- 太长：过度转向，可能偏离轨道
- 建议：100-300ms，根据实际效果调整

### 预期效果验证

#### ✅ 成功指标
- 直角转弯时转弯半径明显减小
- 小车能准确跟随正方形轨道
- 转弯过程平滑，无明显震动
- 转弯后能快速回到正常循迹状态

#### ❌ 需要调整的情况
- 转向过度：减小`TRACK_SQUARE_PIVOT_SPEED`或转向时间
- 转向不足：增大转向速度或延长转向时间
- 误触发：增大`TRACK_SQUARE_PIVOT_THRESHOLD`
- 响应慢：减小阈值或优化检测逻辑
