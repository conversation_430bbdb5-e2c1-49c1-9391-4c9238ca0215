#include "ti_msp_dl_config.h"
#include "bsp_system.h"
#include "Ganway.h"
#include "Ganway_Optimized.h"  // 引入优化循迹算法
#include "Pivot_Turn_Demo.h"   // 引入原地转向演示
#include "key.h"
/********************************************No_Mcu_Demo*******************************************/
/*****************芯片型号 MSPM0G3507 主频80Mhz ***************************************************/
/*****************引脚 AD0:PB0 AD1:PB1 AD2:PB2  ***************************************************/
/*****************OUT PA27*************************************************************************/
/********************************************No_Mcu_Demo*******************************************/
void Way(unsigned char x);
unsigned short Anolog[8]={0};
unsigned short black[8]={100, 100, 100, 100, 100, 100, 100, 100};         // 黑色阈值
unsigned short white[8]={3197, 3117, 3193, 3146, 3154, 3177, 2907, 3171}; // 白色阈值
unsigned short Normal[8];
unsigned char rx_buff[256]={0};
int32_t encoderA_cnt,PWMA,encoderB_cnt,PWMB;
volatile int Flag_stop;
volatile int Flag_stop1;
volatile unsigned int D_Num=0;
volatile int Run = 0;  // 初始化为0，按下PA15按钮后启动
int main(void)
{

	//初始化
	No_MCU_Sensor sensor;
	unsigned char Digtal;
	unsigned short Analog_Values[8];  // 用于存储模拟量数据

    SYSCFG_DL_init();
	int i=0;
    OLED_Init();
	OLED_ColorTurn(1);
	OLED_DisplayTurn(0);
	//jy61pInit();
	DL_Timer_startCounter(PWM_0_INST);
	NVIC_ClearPendingIRQ(ENCODERA_INT_IRQN);
    NVIC_ClearPendingIRQ(ENCODERB_INT_IRQN);
	NVIC_EnableIRQ(ENCODERA_INT_IRQN);
    NVIC_EnableIRQ(ENCODERB_INT_IRQN);
	NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
	NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);

    DL_ADC12_initSingleSample(ADC12_0_INST,
        DL_ADC12_REPEAT_MODE_ENABLED, DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
        DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
    //初始化传感器，不带黑白值
	No_MCU_Ganv_Sensor_Init_Frist(&sensor);
	No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
	Get_Anolog_Value(&sensor,Anolog);

	//也可以自己写按键逻辑完成一键校准功能
	// sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
	// uart0_send_string((char *)rx_buff);
	//delay_ms(100);
	memset(rx_buff,0,256);
	//得到黑白校准值之后，初始化传感器
	No_MCU_Ganv_Sensor_Init(&sensor,white,black);

    //uart0_init();
    OLED_ShowString(0,16,(uint8_t*)"D:",12,1);
    Key_Init_Debounce();  // 初始化按键防抖
    Track_Init();         // 初始化优化循迹系统
    scheduler_init();
	delay_ms(100);
    while (1)
    {
      
		
		
		
		//无时基传感器常规任务，包含模拟量，数字量，归一化量
		No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);

        // 获取传感器数据
        Digtal = Get_Digtal_For_User(&sensor);
        Get_Anolog_Value(&sensor, Analog_Values);  // 获取模拟量数据

		if(Run)
		{
			// 修复：简化启动条件，或者添加初始启动逻辑
			if((D_Num*13252) > encoderB_cnt || encoderB_cnt == 0)
			{
				DL_GPIO_setPins(Stop_PORT, Stop_PIN_4_PIN);

				// 速度一致性验证（调试模式）
				#if TRACK_DEBUG_ENABLE
				bool speed_ok = Motor_Verify_Speed_Consistency();
				if(!speed_ok) {
					// 可以在OLED上显示警告或通过其他方式提示
					// OLED_ShowString(0, 0, "Speed Warning!", 12, 1);
				}
				#endif

				// 选择循迹算法：
				// 方式1：使用原有算法（兼容性测试）
				// Way(Digtal);

				// 方式2：使用优化算法（推荐，解决速度变快问题）
				// Way_With_Analog(Digtal, Analog_Values);

				// 方式3：使用专门优化的算法（针对正方形轨道直角转弯问题）
				Way_Optimized(Digtal, Analog_Values);
			}
		}
		
			
           
        // delay_ms(2);
		// OLED_Clear();
        // OLED_ShowString(0,0,(uint8_t*)"EA:",12,1);
        // OLED_ShowString(38,0,(uint8_t*)"EB:",12,1);
		// OLED_ShowNum(16, 0, encoderA_cnt, 3, 12, 1);
		// OLED_ShowNum(54, 0, encoderB_cnt, 3, 12, 1);
		
		// OLED_ShowString(0,16,(uint8_t*)"D:",12,1);
		// // OLED_ShowString(38,16,(uint8_t*)"Y:",12,1);
		// // OLED_ShowString(76,16,(uint8_t*)"Z:",12,1);
		// OLED_ShowSignedNum(12, 16, D_Num, 1, 12, 1);
		// OLED_ShowSignedNum(52, 16, Sure, 3, 12, 1);
		// // OLED_ShowSignedNum(92, 16, (int)JY61P_Data->z, 3, 12, 1);
        // OLED_Refresh();
		
    }
}

//10ms定时中断
void TIMER_0_INST_IRQHandler(void)
{
    if(DL_TimerA_getPendingInterrupt(TIMER_0_INST))
    {
        if(DL_TIMER_IIDX_ZERO)
        {
			
			Key();//获取当前BLS按键状态
			Key_System_Tick_Inc();  // 系统时钟递增（防抖需要）
			Key_1();
			encoderA_cnt = Get_Encoder_countA;//两个电机安装相反，其中一个编码器值需要相反
			encoderB_cnt = Get_Encoder_countB;
			//Get_Encoder_countA=Get_Encoder_countB=0;
			if((D_Num*13252) < encoderB_cnt)
			{
				DL_GPIO_clearPins(Stop_PORT, Stop_PIN_4_PIN);

			}
			// OLED_Clear();
			// OLED_ShowSignedNum(12, 16, D_Num, 1, 12, 1);
			// OLED_Refresh();
			
			//if(!Flag_Stop)//单击BLS开启或关闭电机
			//{
				// PWMA = -Velocity_A(-10,encoderA_cnt);//PID控制 A右边电机
				// PWMB = -Velocity_B(0,encoderB_cnt);//PID控制	B左边电机
				// Set_PWM(PWMA,PWMB);//PWM波驱动电机
			//}
			// else
			// {
			//  	Set_PWM(0,0);//关闭电机
			// }
			if(Flag_stop == 1)
			{
				D_Num+=1;
				OLED_ShowSignedNum(12, 16, D_Num, 1, 12, 1);
				OLED_Refresh();
				if(D_Num > 5)
				{
					D_Num = 0;
				}

			}
			if(Flag_stop1 == 1)
			{
				Flag_stop1 = 0;  // 清除标志，实现单次触发

				// 调试：LED指示按钮被按下
				DL_GPIO_togglePins(LED_PORT, LED_PIN_22_PIN);

				if(Run == 1)
				{
					Run = 0;
					Set_PWM(0,0);  // 停止电机
				}
				else {
					Run = 1;
					// 添加直接电机测试 - 按下PA15立即启动电机
					Set_PWM(2000, 2000);  // 直接启动电机进行测试
				}

			}
		}
    }
}
