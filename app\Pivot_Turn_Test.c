/**
 * @file Pivot_Turn_Test.c
 * @brief 原地转向测试程序
 * @details 专门测试和演示原地转向功能的效果
 */

#include "Ganway_Optimized.h"
#include "Track_Config.h"
#include "bsp_system.h"

/**
 * @brief 原地转向基础测试
 * @note 测试左转和右转的原地转向效果
 */
void Pivot_Turn_Basic_Test(void)
{
    // 初始化
    Track_Init();
    
    while(1) {
        // 测试右转原地转向
        // OLED_ShowString(0, 0, "Right Pivot", 12, 1);
        // OLED_ShowString(0, 12, "L:2800 R:0", 12, 1);
        Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);  // 右转
        delay_ms(1000);  // 转1秒
        
        // 停止
        Motor_Stop();
        delay_ms(500);
        
        // 测试左转原地转向
        // OLED_ShowString(0, 0, "Left Pivot", 12, 1);
        // OLED_ShowString(0, 12, "L:0 R:2800", 12, 1);
        Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED); // 左转
        delay_ms(1000);  // 转1秒
        
        // 停止
        Motor_Stop();
        delay_ms(500);
        
        // 直行测试
        // OLED_ShowString(0, 0, "Straight", 12, 1);
        // OLED_ShowString(0, 12, "L:3200 R:3200", 12, 1);
        Set_PWM(TRACK_SQUARE_STRAIGHT_SPEED, TRACK_SQUARE_STRAIGHT_SPEED);
        delay_ms(2000);  // 直行2秒
        
        Motor_Stop();
        delay_ms(1000);
    }
}

/**
 * @brief 原地转向角度测试
 * @note 测试不同转向时间对应的转向角度
 */
void Pivot_Turn_Angle_Test(void)
{
    Track_Init();
    static int test_phase = 0;
    
    while(1) {
        switch(test_phase) {
            case 0:
                // 测试90度右转（估算时间）
                // OLED_ShowString(0, 0, "90° Right", 12, 1);
                Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);
                delay_ms(300);  // 根据实际情况调整时间
                Motor_Stop();
                delay_ms(2000);
                break;
                
            case 1:
                // 测试90度左转
                // OLED_ShowString(0, 0, "90° Left", 12, 1);
                Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED);
                delay_ms(300);
                Motor_Stop();
                delay_ms(2000);
                break;
                
            case 2:
                // 测试180度转向
                // OLED_ShowString(0, 0, "180° Turn", 12, 1);
                Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);
                delay_ms(600);  // 大约是90度的2倍时间
                Motor_Stop();
                delay_ms(2000);
                break;
                
            case 3:
                // 测试小角度调整
                // OLED_ShowString(0, 0, "Fine Tune", 12, 1);
                Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED);
                delay_ms(100);  // 小角度调整
                Motor_Stop();
                delay_ms(2000);
                break;
        }
        
        test_phase = (test_phase + 1) % 4;  // 循环测试
        delay_ms(1000);  // 测试间隔
    }
}

/**
 * @brief 原地转向速度对比测试
 * @note 测试不同转向速度的效果
 */
void Pivot_Turn_Speed_Test(void)
{
    Track_Init();
    static int speed_test = 0;
    int test_speeds[] = {2000, 2400, 2800, 3200};  // 不同的测试速度
    
    while(1) {
        int current_speed = test_speeds[speed_test];
        
        // 显示当前测试速度
        // OLED_ShowString(0, 0, "Speed Test", 12, 1);
        // OLED_ShowNum(0, 12, current_speed, 4, 12, 1);
        
        // 右转测试
        Motor_Square_Pivot_Turn(1, current_speed);
        delay_ms(500);
        Motor_Stop();
        delay_ms(500);
        
        // 左转测试
        Motor_Square_Pivot_Turn(-1, current_speed);
        delay_ms(500);
        Motor_Stop();
        delay_ms(1000);
        
        speed_test = (speed_test + 1) % 4;  // 循环测试不同速度
    }
}

/**
 * @brief 传感器触发的原地转向测试
 * @param sensor 传感器对象指针
 * @note 基于传感器输入的实际原地转向测试
 */
void Pivot_Turn_Sensor_Test(No_MCU_Sensor *sensor)
{
    unsigned char digital_data;
    unsigned short analog_data[8];
    Track_Control_t *status;
    static int pivot_count = 0;
    
    Track_Init();
    Track_Set_Mode(TRACK_MODE_ADAPTIVE);
    Track_Set_Speed(TRACK_SQUARE_STRAIGHT_SPEED);
    
    while(1) {
        // 获取传感器数据
        No_Mcu_Ganv_Sensor_Task_Without_tick(sensor);
        digital_data = Get_Digtal_For_User(sensor);
        Get_Anolog_Value(sensor, analog_data);
        
        // 获取状态
        status = Track_Get_Status();
        
        // 手动检测直角转弯条件
        bool need_pivot = Detect_Sharp_Corner(digital_data, status->error);
        
        if(need_pivot) {
            pivot_count++;
            
            // 显示原地转向信息
            // OLED_ShowString(0, 0, "PIVOT MODE", 12, 1);
            // OLED_ShowNum(0, 12, pivot_count, 3, 12, 1);
            
            if(status->error > 0) {
                // 右转原地转向
                // OLED_ShowString(0, 24, "RIGHT PIVOT", 12, 1);
                Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);
            } else {
                // 左转原地转向
                // OLED_ShowString(0, 24, "LEFT PIVOT", 12, 1);
                Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED);
            }
            
            // 原地转向持续时间
            delay_ms(200);  // 可根据实际效果调整
            
        } else {
            // 正常循迹
            // OLED_ShowString(0, 0, "NORMAL", 12, 1);
            // OLED_ShowNum(0, 12, status->error, 3, 12, 1);
            Way_Optimized(digital_data, analog_data);
        }
        
        delay_ms(10);
    }
}

/**
 * @brief 原地转向性能测试
 * @note 测试原地转向的响应时间和精度
 */
void Pivot_Turn_Performance_Test(void)
{
    Track_Init();
    unsigned long start_time, end_time;
    
    while(1) {
        // 测试启动响应时间
        start_time = Get_System_Tick();  // 假设有系统时钟函数
        Motor_Square_Pivot_Turn(1, TRACK_SQUARE_PIVOT_SPEED);
        end_time = Get_System_Tick();
        
        // OLED_ShowString(0, 0, "Start Time:", 12, 1);
        // OLED_ShowNum(0, 12, end_time - start_time, 4, 12, 1);
        
        delay_ms(500);
        
        // 测试停止响应时间
        start_time = Get_System_Tick();
        Motor_Stop();
        end_time = Get_System_Tick();
        
        // OLED_ShowString(0, 24, "Stop Time:", 12, 1);
        // OLED_ShowNum(0, 36, end_time - start_time, 4, 12, 1);
        
        delay_ms(2000);
        
        // 测试方向切换时间
        start_time = Get_System_Tick();
        Motor_Square_Pivot_Turn(-1, TRACK_SQUARE_PIVOT_SPEED);  // 切换到左转
        end_time = Get_System_Tick();
        
        // OLED_ShowString(0, 48, "Switch Time:", 12, 1);
        // OLED_ShowNum(60, 48, end_time - start_time, 4, 12, 1);
        
        delay_ms(500);
        Motor_Stop();
        delay_ms(2000);
    }
}

// 假设的系统时钟函数（需要根据实际系统实现）
unsigned long Get_System_Tick(void)
{
    // 返回系统时钟计数，单位可以是毫秒或微秒
    // 这里需要根据实际的系统时钟实现
    return 0;  // 占位符
}
