******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:08:43 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000033ad


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005718  0001a8e8  R  X
  SRAM                  20200000   00008000  000009af  00007651  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005718   00005718    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003b68   00003b68    r-x .text
  00003c28    00003c28    00001a80   00001a80    r-- .rodata
  000056a8    000056a8    00000070   00000070    r-- .cinit
20200000    20200000    000007b2   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    00000252   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003b68     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000016c     motor.o (.text.Set_PWM)
                  000008ac    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000a08    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000b5c    0000014c     empty.o (.text.main)
                  00000ca8    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000dc8    00000114     motor.o (.text.Motor_Square_Corner_Control)
                  00000edc    00000110     motor.o (.text.Motor_Smooth_Control)
                  00000fec    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000010f8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000011fc    00000100     empty.o (.text.TIMG0_IRQHandler)
                  000012fc    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000013e4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000014c8    000000e2     oled.o (.text.OLED_ShowNum)
                  000015aa    000000de     oled.o (.text.OLED_Init)
                  00001688    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001764    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000183c    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000190c    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  000019c4    000000b8     motor.o (.text.Motor_PID_Control)
                  00001a7c    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001b26    00000002     --HOLE-- [fill = 0]
                  00001b28    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001bd0    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001c78    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001d12    0000009a     oled.o (.text.OLED_ShowString)
                  00001dac    00000090     oled.o (.text.OLED_DrawPoint)
                  00001e3c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001ec8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001f54    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001fe0    00000084     oled.o (.text.OLED_Refresh)
                  00002064    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  000020e8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000216c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000021ee    00000002     --HOLE-- [fill = 0]
                  000021f0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000226c    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  000022e0    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  00002354    00000074     Ganway_Optimized.o (.text.Track_Init)
                  000023c8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000243c    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000024ae    00000002     --HOLE-- [fill = 0]
                  000024b0    0000006c     oled.o (.text.OLED_WR_Byte)
                  0000251c    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002588    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  000025f0    00000068     key.o (.text.Key_1)
                  00002658    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000026c0    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002722    00000002     --HOLE-- [fill = 0]
                  00002724    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002786    00000002     --HOLE-- [fill = 0]
                  00002788    00000060     oled.o (.text.OLED_Clear)
                  000027e8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002846    00000002     --HOLE-- [fill = 0]
                  00002848    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000028a0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000028f4    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002944    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002994    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  000029e0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002a2c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002a76    00000002     --HOLE-- [fill = 0]
                  00002a78    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002ac2    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002b0c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002b54    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002b9c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002be4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002c2c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002c70    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002cb2    00000002     --HOLE-- [fill = 0]
                  00002cb4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002cf4    00000040     key.o (.text.Key)
                  00002d34    00000040     Ganway_Optimized.o (.text.Track_Square_Corner_Control)
                  00002d74    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002db4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002df4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002e30    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002e6c    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00002ea8    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  00002ee4    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  00002f20    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002f5c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002f98    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002fd4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000300e    00000002     --HOLE-- [fill = 0]
                  00003010    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000304a    00000002     --HOLE-- [fill = 0]
                  0000304c    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003084    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000030b8    00000034     oled.o (.text.OLED_ColorTurn)
                  000030ec    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003120    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003154    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00003184    00000030     oled.o (.text.OLED_Pow)
                  000031b4    00000030     systick.o (.text.SysTick_Handler)
                  000031e4    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  00003210    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  0000323c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003268    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  00003294    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  000032bc    00000028     empty.o (.text.DL_Common_updateReg)
                  000032e4    00000028     oled.o (.text.DL_Common_updateReg)
                  0000330c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003334    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  0000335c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003384    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000033ac    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000033d4    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000033fa    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00003420    00000024     motor.o (.text.Left_Control)
                  00003444    00000024     motor.o (.text.Left_Little_Control)
                  00003468    00000024     motor.o (.text.Right_Control)
                  0000348c    00000024     motor.o (.text.Right_Little_Control)
                  000034b0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000034d4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000034f4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003514    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  00003534    00000020     systick.o (.text.delay_ms)
                  00003554    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003572    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003590    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  000035ac    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  000035c8    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000035e4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003600    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000361c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003638    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003654    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003670    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000368c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000036a8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000036c4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000036e0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000036fc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003714    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0000372c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00003744    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000375c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003774    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000378c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000037a4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000037bc    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000037d4    00000018     empty.o (.text.DL_GPIO_setPins)
                  000037ec    00000018     motor.o (.text.DL_GPIO_setPins)
                  00003804    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000381c    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00003834    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000384c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003864    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000387c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003894    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000038ac    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000038c4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000038dc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000038f4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000390c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00003924    00000018     empty.o (.text.DL_Timer_startCounter)
                  0000393c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003954    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000396c    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003984    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  0000399a    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  000039b0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000039c6    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000039dc    00000016     key.o (.text.DL_GPIO_readPins)
                  000039f2    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003a08    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003a1c    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003a30    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003a44    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003a58    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003a6c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003a80    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003a94    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003aa8    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003abc    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003ad0    00000014     key.o (.text.Key_Init_Debounce)
                  00003ae4    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003af6    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003b08    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003b1a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003b2c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003b3e    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003b4e    00000002     --HOLE-- [fill = 0]
                  00003b50    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003b60    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003b70    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003b80    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003b90    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003b9e    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003bac    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003bba    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003bc6    00000002     --HOLE-- [fill = 0]
                  00003bc8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003bd4    0000000c     systick.o (.text.get_systicks)
                  00003be0    0000000c     Scheduler.o (.text.scheduler_init)
                  00003bec    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003bf6    00000002     --HOLE-- [fill = 0]
                  00003bf8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003c00    00000006     libc.a : exit.c.obj (.text:abort)
                  00003c06    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003c0a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003c0e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003c12    00000002     --HOLE-- [fill = 0]
                  00003c14    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003c24    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000056a8    00000070     
                  000056a8    0000004b     (.cinit..data.load) [load image, compression = lzss]
                  000056f3    00000001     --HOLE-- [fill = 0]
                  000056f4    0000000c     (__TI_handler_table)
                  00005700    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005708    00000010     (__TI_cinit_table)

.rodata    0    00003c28    00001a80     
                  00003c28    00000d5c     oled.o (.rodata.asc2_2412)
                  00004984    000005f0     oled.o (.rodata.asc2_1608)
                  00004f74    00000474     oled.o (.rodata.asc2_1206)
                  000053e8    00000228     oled.o (.rodata.asc2_0806)
                  00005610    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005638    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005658    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000566c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00005676    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005678    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005680    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00005688    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00005690    00000006     motor.o (.rodata.str1.10718775090649846465.1)
                  00005696    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00005699    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000569c    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  0000569f    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000056a1    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    00000252     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000024     motor.o (.data.motor_pid)
                  2020073c    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  2020075c    00000010     empty.o (.data.Anolog)
                  2020076c    00000010     empty.o (.data.black)
                  2020077c    00000010     empty.o (.data.white)
                  2020078c    0000000c     key.o (.data.key1_ctrl)
                  20200798    00000008     systick.o (.data.systicks)
                  202007a0    00000004     empty.o (.data.D_Num)
                  202007a4    00000004     empty.o (.data.Run)
                  202007a8    00000004     systick.o (.data.delay_times)
                  202007ac    00000004     key.o (.data.system_tick_ms)
                  202007b0    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b1    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       motor.o                          1600    14        92     
       Ganway_Optimized.o               1408    32        32     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5370    46        157    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       111       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15180   7080      2479   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005708 records: 2, size/record: 8, table size: 16
	.data: load addr=000056a8, load size=0000004b bytes, run addr=20200560, run size=00000252 bytes, compression=lzss
	.bss: load addr=00005700, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000056f4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000033ad     00003c14     00003c0e   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003c07  ADC0_IRQHandler                      
00003c07  ADC1_IRQHandler                      
00003c07  AES_IRQHandler                       
0000190d  Analyze_Track_State                  
2020075c  Anolog                               
00003c0a  C$$EXIT                              
00003c07  CANFD0_IRQHandler                    
00001b29  Calculate_Line_Position              
00003295  Calculate_Position_Error             
00003c07  DAC0_IRQHandler                      
00002cb5  DL_ADC12_setClockConfig              
00003bed  DL_Common_delayCycles                
000027e9  DL_I2C_fillControllerTXFIFO          
000033fb  DL_I2C_setClockConfig                
00001689  DL_SYSCTL_configSYSPLL               
00002c2d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000010f9  DL_Timer_initFourCCPWMMode           
000012fd  DL_Timer_initTimerMode               
000036a9  DL_Timer_setCaptCompUpdateMethod     
0000390d  DL_Timer_setCaptureCompareOutCtl     
00003b61  DL_Timer_setCaptureCompareValue      
000036c5  DL_Timer_setClockConfig              
00002b0d  DL_UART_init                         
00003b09  DL_UART_setClockConfig               
00003c07  DMA_IRQHandler                       
202007a0  D_Num                                
00003c07  Default_Handler                      
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003c07  GROUP0_IRQHandler                    
00000ca9  GROUP1_IRQHandler                    
0000183d  Get_Analog_value                     
00002e6d  Get_Anolog_Value                     
00003b91  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003c0b  HOSTexit                             
0000396d  Handle_Intersection                  
00002589  Handle_Lost_Line                     
00003c07  HardFault_Handler                    
00003c07  I2C0_IRQHandler                      
00003c07  I2C1_IRQHandler                      
00002cf5  Key                                  
000025f1  Key_1                                
00003ad1  Key_Init_Debounce                    
000008ad  Key_Scan_Debounce                    
00003b71  Key_System_Tick_Inc                  
00003421  Left_Control                         
00003445  Left_Little_Control                  
000019c5  Motor_PID_Control                    
00003515  Motor_Reset_Speed_Monitor            
00000edd  Motor_Smooth_Control                 
00001bd1  Motor_Speed_Monitor                  
00000dc9  Motor_Square_Corner_Control          
0000226d  Motor_Verify_Speed_Consistency       
00003c07  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
0000243d  No_MCU_Ganv_Sensor_Init_Frist        
00002c71  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002789  OLED_Clear                           
000030b9  OLED_ColorTurn                       
00002b55  OLED_DisplayTurn                     
00001dad  OLED_DrawPoint                       
20200000  OLED_GRAM                            
000015ab  OLED_Init                            
00003185  OLED_Pow                             
00001fe1  OLED_Refresh                         
000000c1  OLED_ShowChar                        
000014c9  OLED_ShowNum                         
00001c79  OLED_ShowSignedNum                   
00001d13  OLED_ShowString                      
000024b1  OLED_WR_Byte                         
00003c07  PendSV_Handler                       
00003c07  RTC_IRQHandler                       
00003c0f  Reset_Handler                        
00003469  Right_Control                        
0000348d  Right_Little_Control                 
202007a4  Run                                  
00003c07  SPI0_IRQHandler                      
00003c07  SPI1_IRQHandler                      
00003c07  SVC_Handler                          
00002b9d  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
00002849  SYSCFG_DL_I2C_OLED_init              
00001e3d  SYSCFG_DL_PWM_0_init                 
00002be5  SYSCFG_DL_SYSCTL_init                
00003bbb  SYSCFG_DL_SYSTICK_init               
000030ed  SYSCFG_DL_TIMER_0_init               
000028a1  SYSCFG_DL_UART_0_init                
00003121  SYSCFG_DL_init                       
00001ec9  SYSCFG_DL_initPower                  
00000741  Set_PWM                              
000031b5  SysTick_Handler                      
00003c07  TIMA0_IRQHandler                     
00003c07  TIMA1_IRQHandler                     
000011fd  TIMG0_IRQHandler                     
00003c07  TIMG12_IRQHandler                    
00003c07  TIMG6_IRQHandler                     
00003c07  TIMG7_IRQHandler                     
00003c07  TIMG8_IRQHandler                     
00003b1b  TI_memcpy_small                      
00003bad  TI_memset_small                      
000022e1  Track_Adaptive_Control               
00000a09  Track_Basic_Control                  
00002355  Track_Init                           
00002ea9  Track_PID_Control                    
00002d35  Track_Square_Corner_Control          
00002ee5  Track_Weighted_Control               
00002d75  UART0_IRQHandler                     
00003c07  UART1_IRQHandler                     
00003c07  UART2_IRQHandler                     
00003c07  UART3_IRQHandler                     
00002065  Way_Optimized                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005708  __TI_CINIT_Base                      
00005718  __TI_CINIT_Limit                     
00005718  __TI_CINIT_Warm                      
000056f4  __TI_Handler_Table_Base              
00005700  __TI_Handler_Table_Limit             
00002f99  __TI_auto_init_nobinit_nopinit       
000021f1  __TI_decompress_lzss                 
00003b2d  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003b81  __TI_zero_init                       
0000042f  __adddf3                             
0000176f  __addsf3                             
00002a79  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
000026c1  __aeabi_dcmpeq                       
000026fd  __aeabi_dcmpge                       
00002711  __aeabi_dcmpgt                       
000026e9  __aeabi_dcmple                       
000026d5  __aeabi_dcmplt                       
00000fed  __aeabi_ddiv                         
000013e5  __aeabi_dmul                         
00000425  __aeabi_dsub                         
0000304d  __aeabi_f2iz                         
0000176f  __aeabi_fadd                         
00002725  __aeabi_fcmpeq                       
00002761  __aeabi_fcmpge                       
00002775  __aeabi_fcmpgt                       
0000274d  __aeabi_fcmple                       
00002739  __aeabi_fcmplt                       
0000216d  __aeabi_fdiv                         
00001f55  __aeabi_fmul                         
00001765  __aeabi_fsub                         
0000323d  __aeabi_i2d                          
00002f21  __aeabi_i2f                          
000005b7  __aeabi_idiv0                        
00003bc9  __aeabi_memclr                       
00003bc9  __aeabi_memclr4                      
00003bc9  __aeabi_memclr8                      
00003bf9  __aeabi_memcpy                       
00003bf9  __aeabi_memcpy4                      
00003bf9  __aeabi_memcpy8                      
000034b1  __aeabi_ui2d                         
00003385  __aeabi_ui2f                         
00002db5  __aeabi_uidiv                        
00002db5  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002659  __cmpdf2                             
00002fd5  __cmpsf2                             
00000fed  __divdf3                             
0000216d  __divsf3                             
00002659  __eqdf2                              
00002fd5  __eqsf2                              
00002a79  __fixdfsi                            
0000304d  __fixsfsi                            
0000323d  __floatsidf                          
00002f21  __floatsisf                          
000034b1  __floatunsidf                        
00003385  __floatunsisf                        
000023c9  __gedf2                              
00002f5d  __gesf2                              
000023c9  __gtdf2                              
00002f5d  __gtsf2                              
00002659  __ledf2                              
00002fd5  __lesf2                              
00002659  __ltdf2                              
00002fd5  __ltsf2                              
UNDEFED   __mpu_init                           
000013e5  __muldf3                             
00003011  __muldsi3                            
00001f55  __mulsf3                             
00002659  __nedf2                              
00002fd5  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
00001765  __subsf3                             
000033ad  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003c25  _system_pre_init                     
00003c01  abort                                
00002ac3  adc_getValue                         
000053e8  asc2_0806                            
00004f74  asc2_1206                            
00004984  asc2_1608                            
00003c28  asc2_2412                            
ffffffff  binit                                
2020076c  black                                
0000251d  convertAnalogToDigital               
00003535  delay_ms                             
202007a8  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003bd5  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
2020078c  key1_ctrl                            
00000b5d  main                                 
20200718  motor_pid                            
00001a7d  normalizeAnalogValues                
20200560  rx_buff                              
00003be1  scheduler_init                       
00003b9f  strcpy                               
00003269  strncpy                              
2020055c  task_num                             
2020073c  track_ctrl                           
20200660  uart_rx_buffer                       
202007b0  uart_rx_index                        
202007b1  uart_rx_ticks                        
2020077c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Set_PWM                              
000008ad  Key_Scan_Debounce                    
00000a09  Track_Basic_Control                  
00000b5d  main                                 
00000ca9  GROUP1_IRQHandler                    
00000dc9  Motor_Square_Corner_Control          
00000edd  Motor_Smooth_Control                 
00000fed  __aeabi_ddiv                         
00000fed  __divdf3                             
000010f9  DL_Timer_initFourCCPWMMode           
000011fd  TIMG0_IRQHandler                     
000012fd  DL_Timer_initTimerMode               
000013e5  __aeabi_dmul                         
000013e5  __muldf3                             
000014c9  OLED_ShowNum                         
000015ab  OLED_Init                            
00001689  DL_SYSCTL_configSYSPLL               
00001765  __aeabi_fsub                         
00001765  __subsf3                             
0000176f  __addsf3                             
0000176f  __aeabi_fadd                         
0000183d  Get_Analog_value                     
0000190d  Analyze_Track_State                  
000019c5  Motor_PID_Control                    
00001a7d  normalizeAnalogValues                
00001b29  Calculate_Line_Position              
00001bd1  Motor_Speed_Monitor                  
00001c79  OLED_ShowSignedNum                   
00001d13  OLED_ShowString                      
00001dad  OLED_DrawPoint                       
00001e3d  SYSCFG_DL_PWM_0_init                 
00001ec9  SYSCFG_DL_initPower                  
00001f55  __aeabi_fmul                         
00001f55  __mulsf3                             
00001fe1  OLED_Refresh                         
00002065  Way_Optimized                        
0000216d  __aeabi_fdiv                         
0000216d  __divsf3                             
000021f1  __TI_decompress_lzss                 
0000226d  Motor_Verify_Speed_Consistency       
000022e1  Track_Adaptive_Control               
00002355  Track_Init                           
000023c9  __gedf2                              
000023c9  __gtdf2                              
0000243d  No_MCU_Ganv_Sensor_Init_Frist        
000024b1  OLED_WR_Byte                         
0000251d  convertAnalogToDigital               
00002589  Handle_Lost_Line                     
000025f1  Key_1                                
00002659  __cmpdf2                             
00002659  __eqdf2                              
00002659  __ledf2                              
00002659  __ltdf2                              
00002659  __nedf2                              
000026c1  __aeabi_dcmpeq                       
000026d5  __aeabi_dcmplt                       
000026e9  __aeabi_dcmple                       
000026fd  __aeabi_dcmpge                       
00002711  __aeabi_dcmpgt                       
00002725  __aeabi_fcmpeq                       
00002739  __aeabi_fcmplt                       
0000274d  __aeabi_fcmple                       
00002761  __aeabi_fcmpge                       
00002775  __aeabi_fcmpgt                       
00002789  OLED_Clear                           
000027e9  DL_I2C_fillControllerTXFIFO          
00002849  SYSCFG_DL_I2C_OLED_init              
000028a1  SYSCFG_DL_UART_0_init                
00002a79  __aeabi_d2iz                         
00002a79  __fixdfsi                            
00002ac3  adc_getValue                         
00002b0d  DL_UART_init                         
00002b55  OLED_DisplayTurn                     
00002b9d  SYSCFG_DL_ADC12_0_init               
00002be5  SYSCFG_DL_SYSCTL_init                
00002c2d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002c71  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002cb5  DL_ADC12_setClockConfig              
00002cf5  Key                                  
00002d35  Track_Square_Corner_Control          
00002d75  UART0_IRQHandler                     
00002db5  __aeabi_uidiv                        
00002db5  __aeabi_uidivmod                     
00002e6d  Get_Anolog_Value                     
00002ea9  Track_PID_Control                    
00002ee5  Track_Weighted_Control               
00002f21  __aeabi_i2f                          
00002f21  __floatsisf                          
00002f5d  __gesf2                              
00002f5d  __gtsf2                              
00002f99  __TI_auto_init_nobinit_nopinit       
00002fd5  __cmpsf2                             
00002fd5  __eqsf2                              
00002fd5  __lesf2                              
00002fd5  __ltsf2                              
00002fd5  __nesf2                              
00003011  __muldsi3                            
0000304d  __aeabi_f2iz                         
0000304d  __fixsfsi                            
000030b9  OLED_ColorTurn                       
000030ed  SYSCFG_DL_TIMER_0_init               
00003121  SYSCFG_DL_init                       
00003185  OLED_Pow                             
000031b5  SysTick_Handler                      
0000323d  __aeabi_i2d                          
0000323d  __floatsidf                          
00003269  strncpy                              
00003295  Calculate_Position_Error             
00003385  __aeabi_ui2f                         
00003385  __floatunsisf                        
000033ad  _c_int00_noargs                      
000033fb  DL_I2C_setClockConfig                
00003421  Left_Control                         
00003445  Left_Little_Control                  
00003469  Right_Control                        
0000348d  Right_Little_Control                 
000034b1  __aeabi_ui2d                         
000034b1  __floatunsidf                        
00003515  Motor_Reset_Speed_Monitor            
00003535  delay_ms                             
000036a9  DL_Timer_setCaptCompUpdateMethod     
000036c5  DL_Timer_setClockConfig              
0000390d  DL_Timer_setCaptureCompareOutCtl     
0000396d  Handle_Intersection                  
00003ad1  Key_Init_Debounce                    
00003b09  DL_UART_setClockConfig               
00003b1b  TI_memcpy_small                      
00003b2d  __TI_decompress_none                 
00003b61  DL_Timer_setCaptureCompareValue      
00003b71  Key_System_Tick_Inc                  
00003b81  __TI_zero_init                       
00003b91  Get_Digtal_For_User                  
00003b9f  strcpy                               
00003bad  TI_memset_small                      
00003bbb  SYSCFG_DL_SYSTICK_init               
00003bc9  __aeabi_memclr                       
00003bc9  __aeabi_memclr4                      
00003bc9  __aeabi_memclr8                      
00003bd5  get_systicks                         
00003be1  scheduler_init                       
00003bed  DL_Common_delayCycles                
00003bf9  __aeabi_memcpy                       
00003bf9  __aeabi_memcpy4                      
00003bf9  __aeabi_memcpy8                      
00003c01  abort                                
00003c07  ADC0_IRQHandler                      
00003c07  ADC1_IRQHandler                      
00003c07  AES_IRQHandler                       
00003c07  CANFD0_IRQHandler                    
00003c07  DAC0_IRQHandler                      
00003c07  DMA_IRQHandler                       
00003c07  Default_Handler                      
00003c07  GROUP0_IRQHandler                    
00003c07  HardFault_Handler                    
00003c07  I2C0_IRQHandler                      
00003c07  I2C1_IRQHandler                      
00003c07  NMI_Handler                          
00003c07  PendSV_Handler                       
00003c07  RTC_IRQHandler                       
00003c07  SPI0_IRQHandler                      
00003c07  SPI1_IRQHandler                      
00003c07  SVC_Handler                          
00003c07  TIMA0_IRQHandler                     
00003c07  TIMA1_IRQHandler                     
00003c07  TIMG12_IRQHandler                    
00003c07  TIMG6_IRQHandler                     
00003c07  TIMG7_IRQHandler                     
00003c07  TIMG8_IRQHandler                     
00003c07  UART1_IRQHandler                     
00003c07  UART2_IRQHandler                     
00003c07  UART3_IRQHandler                     
00003c0a  C$$EXIT                              
00003c0b  HOSTexit                             
00003c0f  Reset_Handler                        
00003c25  _system_pre_init                     
00003c28  asc2_2412                            
00004984  asc2_1608                            
00004f74  asc2_1206                            
000053e8  asc2_0806                            
000056f4  __TI_Handler_Table_Base              
00005700  __TI_Handler_Table_Limit             
00005708  __TI_CINIT_Base                      
00005718  __TI_CINIT_Limit                     
00005718  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  motor_pid                            
2020073c  track_ctrl                           
2020075c  Anolog                               
2020076c  black                                
2020077c  white                                
2020078c  key1_ctrl                            
202007a0  D_Num                                
202007a4  Run                                  
202007a8  delay_times                          
202007b0  uart_rx_index                        
202007b1  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[241 symbols]
