******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 18:26:22 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003579


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005900  0001a700  R  X
  SRAM                  20200000   00008000  000009b3  0000764d  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005900   00005900    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003d38   00003d38    r-x .text
  00003df8    00003df8    00001a98   00001a98    r-- .rodata
  00005890    00005890    00000070   00000070    r-- .cinit
20200000    20200000    000007b6   00000000    rw-
  20200000    20200000    0000055d   00000000    rw- .bss
  20200560    20200560    00000256   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003d38     
                  000000c0    000001d0     oled.o (.text.OLED_ShowChar)
                  00000290    00000194     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000424    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000005b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000005b8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000740    0000016c     motor.o (.text.Set_PWM)
                  000008ac    0000015c     key.o (.text.Key_Scan_Debounce)
                  00000a08    00000154     Ganway_Optimized.o (.text.Track_Basic_Control)
                  00000b5c    0000014c     empty.o (.text.main)
                  00000ca8    00000144     Ganway_Optimized.o (.text.Detect_Sharp_Corner)
                  00000dec    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000f0c    00000110     motor.o (.text.Motor_Smooth_Control)
                  0000101c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001128    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000122c    00000100     empty.o (.text.TIMG0_IRQHandler)
                  0000132c    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001414    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000014f8    000000e2     oled.o (.text.OLED_ShowNum)
                  000015da    000000de     oled.o (.text.OLED_Init)
                  000016b8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001794    000000dc     motor.o (.text.Motor_Square_Corner_Control)
                  00001870    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001948    000000d0     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00001a18    000000b8     Ganway_Optimized.o (.text.Analyze_Track_State)
                  00001ad0    000000b8     motor.o (.text.Motor_PID_Control)
                  00001b88    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00001c32    00000002     --HOLE-- [fill = 0]
                  00001c34    000000a8     Ganway_Optimized.o (.text.Calculate_Line_Position)
                  00001cdc    000000a8     motor.o (.text.Motor_Speed_Monitor)
                  00001d84    0000009a     oled.o (.text.OLED_ShowSignedNum)
                  00001e1e    0000009a     oled.o (.text.OLED_ShowString)
                  00001eb8    00000090     oled.o (.text.OLED_DrawPoint)
                  00001f48    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00001fd4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002060    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000020ec    00000088     Ganway_Optimized.o (.text.Track_Square_Corner_Control)
                  00002174    00000084     oled.o (.text.OLED_Refresh)
                  000021f8    00000084     Ganway_Optimized.o (.text.Way_Optimized)
                  0000227c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002300    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002382    00000002     --HOLE-- [fill = 0]
                  00002384    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002400    00000078     motor.o (.text.Motor_Square_Pivot_Turn)
                  00002478    00000074     motor.o (.text.Motor_Verify_Speed_Consistency)
                  000024ec    00000074     Ganway_Optimized.o (.text.Track_Adaptive_Control)
                  00002560    00000074     Ganway_Optimized.o (.text.Track_Init)
                  000025d4    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002648    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000026ba    00000002     --HOLE-- [fill = 0]
                  000026bc    0000006c     oled.o (.text.OLED_WR_Byte)
                  00002728    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00002794    00000068     Ganway_Optimized.o (.text.Handle_Lost_Line)
                  000027fc    00000068     key.o (.text.Key_1)
                  00002864    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000028cc    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000292e    00000002     --HOLE-- [fill = 0]
                  00002930    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002992    00000002     --HOLE-- [fill = 0]
                  00002994    00000060     oled.o (.text.OLED_Clear)
                  000029f4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00002a52    00000002     --HOLE-- [fill = 0]
                  00002a54    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00002aac    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002b00    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00002b50    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00002ba0    0000004c     empty.o (.text.DL_ADC12_initSingleSample)
                  00002bec    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002c38    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00002c82    00000002     --HOLE-- [fill = 0]
                  00002c84    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002cce    0000004a     No_Mcu_Ganv_Grayscale_Sensor.o (.text.adc_getValue)
                  00002d18    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002d60    00000048     oled.o (.text.OLED_DisplayTurn)
                  00002da8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00002df0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002e38    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002e7c    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00002ebe    00000002     --HOLE-- [fill = 0]
                  00002ec0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00002f00    00000040     key.o (.text.Key)
                  00002f40    00000040     bsp_usart.o (.text.UART0_IRQHandler)
                  00002f80    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002fc0    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00002ffc    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003038    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00003074    0000003c     Ganway_Optimized.o (.text.Track_PID_Control)
                  000030b0    0000003c     Ganway_Optimized.o (.text.Track_Weighted_Control)
                  000030ec    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00003128    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003164    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000031a0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000031da    00000002     --HOLE-- [fill = 0]
                  000031dc    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00003216    00000002     --HOLE-- [fill = 0]
                  00003218    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003250    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003284    00000034     oled.o (.text.OLED_ColorTurn)
                  000032b8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000032ec    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003320    00000030     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getMemResult)
                  00003350    00000030     oled.o (.text.OLED_Pow)
                  00003380    00000030     systick.o (.text.SysTick_Handler)
                  000033b0    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  000033dc    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00003408    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003434    0000002c     libc.a : strncpy.c.obj (.text.strncpy)
                  00003460    00000028     Ganway_Optimized.o (.text.Calculate_Position_Error)
                  00003488    00000028     empty.o (.text.DL_Common_updateReg)
                  000034b0    00000028     oled.o (.text.DL_Common_updateReg)
                  000034d8    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003500    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00003528    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00003550    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00003578    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000035a0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000035c6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000035ec    00000024     motor.o (.text.Left_Control)
                  00003610    00000024     motor.o (.text.Left_Little_Control)
                  00003634    00000024     motor.o (.text.Right_Control)
                  00003658    00000024     motor.o (.text.Right_Little_Control)
                  0000367c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000036a0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000036c0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000036e0    00000020     motor.o (.text.Motor_Reset_Speed_Monitor)
                  00003700    00000020     systick.o (.text.delay_ms)
                  00003720    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000373e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000375c    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_startConversion)
                  00003778    0000001c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_stopConversion)
                  00003794    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000037b0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000037cc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000037e8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003804    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003820    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000383c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003858    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003874    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003890    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000038ac    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000038c8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000038e0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000038f8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00003910    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003928    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003940    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003958    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003970    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003988    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000039a0    00000018     empty.o (.text.DL_GPIO_setPins)
                  000039b8    00000018     motor.o (.text.DL_GPIO_setPins)
                  000039d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000039e8    00000018     empty.o (.text.DL_GPIO_togglePins)
                  00003a00    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00003a18    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00003a30    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00003a48    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00003a60    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00003a78    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00003a90    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003aa8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003ac0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00003ad8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00003af0    00000018     empty.o (.text.DL_Timer_startCounter)
                  00003b08    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00003b20    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003b38    00000018     Ganway_Optimized.o (.text.Handle_Intersection)
                  00003b50    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_disableConversions)
                  00003b66    00000016     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_enableConversions)
                  00003b7c    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00003b92    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00003ba8    00000016     key.o (.text.DL_GPIO_readPins)
                  00003bbe    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003bd4    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00003be8    00000014     empty.o (.text.DL_GPIO_clearPins)
                  00003bfc    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00003c10    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003c24    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00003c38    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00003c4c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00003c60    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00003c74    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003c88    00000014     bsp_usart.o (.text.DL_UART_receiveData)
                  00003c9c    00000014     key.o (.text.Key_Init_Debounce)
                  00003cb0    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00003cc2    00000012     bsp_usart.o (.text.DL_UART_getPendingInterrupt)
                  00003cd4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003ce6    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003cf8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003d0a    00000010     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_ADC12_getStatus)
                  00003d1a    00000002     --HOLE-- [fill = 0]
                  00003d1c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00003d2c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00003d3c    00000010     key.o (.text.Key_System_Tick_Inc)
                  00003d4c    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003d5c    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00003d6a    0000000e     libc.a : strcpy.c.obj (.text.strcpy)
                  00003d78    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003d86    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003d92    00000002     --HOLE-- [fill = 0]
                  00003d94    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003da0    0000000c     systick.o (.text.get_systicks)
                  00003dac    0000000c     Scheduler.o (.text.scheduler_init)
                  00003db8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003dc2    00000002     --HOLE-- [fill = 0]
                  00003dc4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003dcc    00000006     libc.a : exit.c.obj (.text:abort)
                  00003dd2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003dd6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003dda    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003dde    00000002     --HOLE-- [fill = 0]
                  00003de0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00003df0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00003df4    00000004     --HOLE-- [fill = 0]

.cinit     0    00005890    00000070     
                  00005890    0000004b     (.cinit..data.load) [load image, compression = lzss]
                  000058db    00000001     --HOLE-- [fill = 0]
                  000058dc    0000000c     (__TI_handler_table)
                  000058e8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000058f0    00000010     (__TI_cinit_table)

.rodata    0    00003df8    00001a98     
                  00003df8    00000d5c     oled.o (.rodata.asc2_2412)
                  00004b54    000005f0     oled.o (.rodata.asc2_1608)
                  00005144    00000474     oled.o (.rodata.asc2_1206)
                  000055b8    00000228     oled.o (.rodata.asc2_0806)
                  000057e0    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005808    00000020     Ganway_Optimized.o (.rodata.sensor_weights)
                  00005828    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000583c    0000000d     motor.o (.rodata.str1.7408246413643671320.1)
                  00005849    0000000b     motor.o (.rodata.str1.5725326084866254091.1)
                  00005854    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000585e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00005860    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00005868    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00005870    00000008     motor.o (.rodata.str1.5850567729483738290.1)
                  00005878    00000006     motor.o (.rodata.str1.4833480291612602813.1)
                  0000587e    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00005881    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005884    00000003     empty.o (.rodata.str1.9517790425240694019.1)
                  00005887    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005889    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000055d     UNINITIALIZED
                  20200000    00000480     (.common:OLED_GRAM)
                  20200480    000000bc     (.common:gPWM_0Backup)
                  2020053c    00000004     (.common:Flag_stop)
                  20200540    00000004     (.common:Flag_stop1)
                  20200544    00000004     (.common:Get_Encoder_countA)
                  20200548    00000004     (.common:Get_Encoder_countB)
                  2020054c    00000004     (.common:encoderA_cnt)
                  20200550    00000004     (.common:encoderB_cnt)
                  20200554    00000004     (.common:gpio_interrup1)
                  20200558    00000004     (.common:gpio_interrup2)
                  2020055c    00000001     (.common:task_num)

.data      0    20200560    00000256     UNINITIALIZED
                  20200560    00000100     empty.o (.data.rx_buff)
                  20200660    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  202006e0    00000038     motor.o (.data.speed_monitor)
                  20200718    00000024     motor.o (.data.motor_pid)
                  2020073c    00000020     Ganway_Optimized.o (.data.track_ctrl)
                  2020075c    00000010     empty.o (.data.Anolog)
                  2020076c    00000010     empty.o (.data.black)
                  2020077c    00000010     empty.o (.data.white)
                  2020078c    0000000c     key.o (.data.key1_ctrl)
                  20200798    00000008     systick.o (.data.systicks)
                  202007a0    00000004     empty.o (.data.D_Num)
                  202007a4    00000004     Ganway_Optimized.o (.data.Detect_Sharp_Corner.sharp_corner_count)
                  202007a8    00000004     empty.o (.data.Run)
                  202007ac    00000004     systick.o (.data.delay_times)
                  202007b0    00000004     key.o (.data.system_tick_ms)
                  202007b4    00000001     bsp_usart.o (.data.uart_rx_index)
                  202007b5    00000001     bsp_usart.o (.data.uart_rx_ticks)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               2772    96        188    
       empty.o                          902     3         328    
       startup_mspm0g350x_ticlang.o     8       192       0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3682    291       516    
                                                                 
    .\app\
       Ganway_Optimized.o               1804    32        36     
       motor.o                          1664    38        92     
       No_Mcu_Ganv_Grayscale_Sensor.o   1414    0         0      
       key.o                            574     0         16     
       encoder.o                        362     0         16     
       Scheduler.o                      12      0         1      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5830    70        161    
                                                                 
    .\app\OLED\
       oled.o                           2012    6632      1152   
    +--+--------------------------------+-------+---------+---------+
       Total:                           2012    6632      1152   
                                                                 
    .\bsp\
       bsp_usart.o                      102     0         130    
       systick.o                        92      0         12     
    +--+--------------------------------+-------+---------+---------+
       Total:                           194     0         142    
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                       588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o     288     0         0      
       dl_i2c.o                         132     0         0      
       dl_uart.o                        90      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1172    0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj       124     0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       strncpy.c.obj                    44      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       copy_zero_init.c.obj             16      0         0      
       memset16.S.obj                   14      0         0      
       strcpy.c.obj                     14      0         0      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           374     0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402     0         0      
       divdf3.S.obj                     268     0         0      
       muldf3.S.obj                     228     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       fixdfsi.S.obj                    74      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       aeabi_memset.S.obj               12      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 2       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2372    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       111       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     15640   7104      2483   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000058f0 records: 2, size/record: 8, table size: 16
	.data: load addr=00005890, load size=0000004b bytes, run addr=20200560, run size=00000256 bytes, compression=lzss
	.bss: load addr=000058e8, load size=00000008 bytes, run addr=20200000, run size=0000055d bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000058dc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00003579     00003de0     00003dda   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003dd3  ADC0_IRQHandler                      
00003dd3  ADC1_IRQHandler                      
00003dd3  AES_IRQHandler                       
00001a19  Analyze_Track_State                  
2020075c  Anolog                               
00003dd6  C$$EXIT                              
00003dd3  CANFD0_IRQHandler                    
00001c35  Calculate_Line_Position              
00003461  Calculate_Position_Error             
00003dd3  DAC0_IRQHandler                      
00002ec1  DL_ADC12_setClockConfig              
00003db9  DL_Common_delayCycles                
000029f5  DL_I2C_fillControllerTXFIFO          
000035c7  DL_I2C_setClockConfig                
000016b9  DL_SYSCTL_configSYSPLL               
00002e39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001129  DL_Timer_initFourCCPWMMode           
0000132d  DL_Timer_initTimerMode               
00003875  DL_Timer_setCaptCompUpdateMethod     
00003ad9  DL_Timer_setCaptureCompareOutCtl     
00003d2d  DL_Timer_setCaptureCompareValue      
00003891  DL_Timer_setClockConfig              
00002d19  DL_UART_init                         
00003cd5  DL_UART_setClockConfig               
00003dd3  DMA_IRQHandler                       
202007a0  D_Num                                
00003dd3  Default_Handler                      
00000ca9  Detect_Sharp_Corner                  
2020053c  Flag_stop                            
20200540  Flag_stop1                           
00003dd3  GROUP0_IRQHandler                    
00000ded  GROUP1_IRQHandler                    
00001949  Get_Analog_value                     
00003039  Get_Anolog_Value                     
00003d5d  Get_Digtal_For_User                  
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
00003dd7  HOSTexit                             
00003b39  Handle_Intersection                  
00002795  Handle_Lost_Line                     
00003dd3  HardFault_Handler                    
00003dd3  I2C0_IRQHandler                      
00003dd3  I2C1_IRQHandler                      
00002f01  Key                                  
000027fd  Key_1                                
00003c9d  Key_Init_Debounce                    
000008ad  Key_Scan_Debounce                    
00003d3d  Key_System_Tick_Inc                  
000035ed  Left_Control                         
00003611  Left_Little_Control                  
00001ad1  Motor_PID_Control                    
000036e1  Motor_Reset_Speed_Monitor            
00000f0d  Motor_Smooth_Control                 
00001cdd  Motor_Speed_Monitor                  
00001795  Motor_Square_Corner_Control          
00002401  Motor_Square_Pivot_Turn              
00002479  Motor_Verify_Speed_Consistency       
00003dd3  NMI_Handler                          
000005b9  No_MCU_Ganv_Sensor_Init              
00002649  No_MCU_Ganv_Sensor_Init_Frist        
00002e7d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002995  OLED_Clear                           
00003285  OLED_ColorTurn                       
00002d61  OLED_DisplayTurn                     
00001eb9  OLED_DrawPoint                       
20200000  OLED_GRAM                            
000015db  OLED_Init                            
00003351  OLED_Pow                             
00002175  OLED_Refresh                         
000000c1  OLED_ShowChar                        
000014f9  OLED_ShowNum                         
00001d85  OLED_ShowSignedNum                   
00001e1f  OLED_ShowString                      
000026bd  OLED_WR_Byte                         
00003dd3  PendSV_Handler                       
00003dd3  RTC_IRQHandler                       
00003ddb  Reset_Handler                        
00003635  Right_Control                        
00003659  Right_Little_Control                 
202007a8  Run                                  
00003dd3  SPI0_IRQHandler                      
00003dd3  SPI1_IRQHandler                      
00003dd3  SVC_Handler                          
00002da9  SYSCFG_DL_ADC12_0_init               
00000291  SYSCFG_DL_GPIO_init                  
00002a55  SYSCFG_DL_I2C_OLED_init              
00001f49  SYSCFG_DL_PWM_0_init                 
00002df1  SYSCFG_DL_SYSCTL_init                
00003d87  SYSCFG_DL_SYSTICK_init               
000032b9  SYSCFG_DL_TIMER_0_init               
00002aad  SYSCFG_DL_UART_0_init                
000032ed  SYSCFG_DL_init                       
00001fd5  SYSCFG_DL_initPower                  
00000741  Set_PWM                              
00003381  SysTick_Handler                      
00003dd3  TIMA0_IRQHandler                     
00003dd3  TIMA1_IRQHandler                     
0000122d  TIMG0_IRQHandler                     
00003dd3  TIMG12_IRQHandler                    
00003dd3  TIMG6_IRQHandler                     
00003dd3  TIMG7_IRQHandler                     
00003dd3  TIMG8_IRQHandler                     
00003ce7  TI_memcpy_small                      
00003d79  TI_memset_small                      
000024ed  Track_Adaptive_Control               
00000a09  Track_Basic_Control                  
00002561  Track_Init                           
00003075  Track_PID_Control                    
000020ed  Track_Square_Corner_Control          
000030b1  Track_Weighted_Control               
00002f41  UART0_IRQHandler                     
00003dd3  UART1_IRQHandler                     
00003dd3  UART2_IRQHandler                     
00003dd3  UART3_IRQHandler                     
000021f9  Way_Optimized                        
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
000058f0  __TI_CINIT_Base                      
00005900  __TI_CINIT_Limit                     
00005900  __TI_CINIT_Warm                      
000058dc  __TI_Handler_Table_Base              
000058e8  __TI_Handler_Table_Limit             
00003165  __TI_auto_init_nobinit_nopinit       
00002385  __TI_decompress_lzss                 
00003cf9  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00003d4d  __TI_zero_init                       
0000042f  __adddf3                             
0000187b  __addsf3                             
00002c85  __aeabi_d2iz                         
0000042f  __aeabi_dadd                         
000028cd  __aeabi_dcmpeq                       
00002909  __aeabi_dcmpge                       
0000291d  __aeabi_dcmpgt                       
000028f5  __aeabi_dcmple                       
000028e1  __aeabi_dcmplt                       
0000101d  __aeabi_ddiv                         
00001415  __aeabi_dmul                         
00000425  __aeabi_dsub                         
00003219  __aeabi_f2iz                         
0000187b  __aeabi_fadd                         
00002931  __aeabi_fcmpeq                       
0000296d  __aeabi_fcmpge                       
00002981  __aeabi_fcmpgt                       
00002959  __aeabi_fcmple                       
00002945  __aeabi_fcmplt                       
00002301  __aeabi_fdiv                         
00002061  __aeabi_fmul                         
00001871  __aeabi_fsub                         
00003409  __aeabi_i2d                          
000030ed  __aeabi_i2f                          
000005b7  __aeabi_idiv0                        
00003d95  __aeabi_memclr                       
00003d95  __aeabi_memclr4                      
00003d95  __aeabi_memclr8                      
00003dc5  __aeabi_memcpy                       
00003dc5  __aeabi_memcpy4                      
00003dc5  __aeabi_memcpy8                      
0000367d  __aeabi_ui2d                         
00003551  __aeabi_ui2f                         
00002f81  __aeabi_uidiv                        
00002f81  __aeabi_uidivmod                     
ffffffff  __binit__                            
00002865  __cmpdf2                             
000031a1  __cmpsf2                             
0000101d  __divdf3                             
00002301  __divsf3                             
00002865  __eqdf2                              
000031a1  __eqsf2                              
00002c85  __fixdfsi                            
00003219  __fixsfsi                            
00003409  __floatsidf                          
000030ed  __floatsisf                          
0000367d  __floatunsidf                        
00003551  __floatunsisf                        
000025d5  __gedf2                              
00003129  __gesf2                              
000025d5  __gtdf2                              
00003129  __gtsf2                              
00002865  __ledf2                              
000031a1  __lesf2                              
00002865  __ltdf2                              
000031a1  __ltsf2                              
UNDEFED   __mpu_init                           
00001415  __muldf3                             
000031dd  __muldsi3                            
00002061  __mulsf3                             
00002865  __nedf2                              
000031a1  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00000425  __subdf3                             
00001871  __subsf3                             
00003579  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003df1  _system_pre_init                     
00003dcd  abort                                
00002ccf  adc_getValue                         
000055b8  asc2_0806                            
00005144  asc2_1206                            
00004b54  asc2_1608                            
00003df8  asc2_2412                            
ffffffff  binit                                
2020076c  black                                
00002729  convertAnalogToDigital               
00003701  delay_ms                             
202007ac  delay_times                          
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200480  gPWM_0Backup                         
00003da1  get_systicks                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
00000000  interruptVectors                     
2020078c  key1_ctrl                            
00000b5d  main                                 
20200718  motor_pid                            
00001b89  normalizeAnalogValues                
20200560  rx_buff                              
00003dad  scheduler_init                       
00003d6b  strcpy                               
00003435  strncpy                              
2020055c  task_num                             
2020073c  track_ctrl                           
20200660  uart_rx_buffer                       
202007b4  uart_rx_index                        
202007b5  uart_rx_ticks                        
2020077c  white                                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  OLED_ShowChar                        
00000200  __STACK_SIZE                         
00000291  SYSCFG_DL_GPIO_init                  
00000425  __aeabi_dsub                         
00000425  __subdf3                             
0000042f  __adddf3                             
0000042f  __aeabi_dadd                         
000005b7  __aeabi_idiv0                        
000005b9  No_MCU_Ganv_Sensor_Init              
00000741  Set_PWM                              
000008ad  Key_Scan_Debounce                    
00000a09  Track_Basic_Control                  
00000b5d  main                                 
00000ca9  Detect_Sharp_Corner                  
00000ded  GROUP1_IRQHandler                    
00000f0d  Motor_Smooth_Control                 
0000101d  __aeabi_ddiv                         
0000101d  __divdf3                             
00001129  DL_Timer_initFourCCPWMMode           
0000122d  TIMG0_IRQHandler                     
0000132d  DL_Timer_initTimerMode               
00001415  __aeabi_dmul                         
00001415  __muldf3                             
000014f9  OLED_ShowNum                         
000015db  OLED_Init                            
000016b9  DL_SYSCTL_configSYSPLL               
00001795  Motor_Square_Corner_Control          
00001871  __aeabi_fsub                         
00001871  __subsf3                             
0000187b  __addsf3                             
0000187b  __aeabi_fadd                         
00001949  Get_Analog_value                     
00001a19  Analyze_Track_State                  
00001ad1  Motor_PID_Control                    
00001b89  normalizeAnalogValues                
00001c35  Calculate_Line_Position              
00001cdd  Motor_Speed_Monitor                  
00001d85  OLED_ShowSignedNum                   
00001e1f  OLED_ShowString                      
00001eb9  OLED_DrawPoint                       
00001f49  SYSCFG_DL_PWM_0_init                 
00001fd5  SYSCFG_DL_initPower                  
00002061  __aeabi_fmul                         
00002061  __mulsf3                             
000020ed  Track_Square_Corner_Control          
00002175  OLED_Refresh                         
000021f9  Way_Optimized                        
00002301  __aeabi_fdiv                         
00002301  __divsf3                             
00002385  __TI_decompress_lzss                 
00002401  Motor_Square_Pivot_Turn              
00002479  Motor_Verify_Speed_Consistency       
000024ed  Track_Adaptive_Control               
00002561  Track_Init                           
000025d5  __gedf2                              
000025d5  __gtdf2                              
00002649  No_MCU_Ganv_Sensor_Init_Frist        
000026bd  OLED_WR_Byte                         
00002729  convertAnalogToDigital               
00002795  Handle_Lost_Line                     
000027fd  Key_1                                
00002865  __cmpdf2                             
00002865  __eqdf2                              
00002865  __ledf2                              
00002865  __ltdf2                              
00002865  __nedf2                              
000028cd  __aeabi_dcmpeq                       
000028e1  __aeabi_dcmplt                       
000028f5  __aeabi_dcmple                       
00002909  __aeabi_dcmpge                       
0000291d  __aeabi_dcmpgt                       
00002931  __aeabi_fcmpeq                       
00002945  __aeabi_fcmplt                       
00002959  __aeabi_fcmple                       
0000296d  __aeabi_fcmpge                       
00002981  __aeabi_fcmpgt                       
00002995  OLED_Clear                           
000029f5  DL_I2C_fillControllerTXFIFO          
00002a55  SYSCFG_DL_I2C_OLED_init              
00002aad  SYSCFG_DL_UART_0_init                
00002c85  __aeabi_d2iz                         
00002c85  __fixdfsi                            
00002ccf  adc_getValue                         
00002d19  DL_UART_init                         
00002d61  OLED_DisplayTurn                     
00002da9  SYSCFG_DL_ADC12_0_init               
00002df1  SYSCFG_DL_SYSCTL_init                
00002e39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002e7d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002ec1  DL_ADC12_setClockConfig              
00002f01  Key                                  
00002f41  UART0_IRQHandler                     
00002f81  __aeabi_uidiv                        
00002f81  __aeabi_uidivmod                     
00003039  Get_Anolog_Value                     
00003075  Track_PID_Control                    
000030b1  Track_Weighted_Control               
000030ed  __aeabi_i2f                          
000030ed  __floatsisf                          
00003129  __gesf2                              
00003129  __gtsf2                              
00003165  __TI_auto_init_nobinit_nopinit       
000031a1  __cmpsf2                             
000031a1  __eqsf2                              
000031a1  __lesf2                              
000031a1  __ltsf2                              
000031a1  __nesf2                              
000031dd  __muldsi3                            
00003219  __aeabi_f2iz                         
00003219  __fixsfsi                            
00003285  OLED_ColorTurn                       
000032b9  SYSCFG_DL_TIMER_0_init               
000032ed  SYSCFG_DL_init                       
00003351  OLED_Pow                             
00003381  SysTick_Handler                      
00003409  __aeabi_i2d                          
00003409  __floatsidf                          
00003435  strncpy                              
00003461  Calculate_Position_Error             
00003551  __aeabi_ui2f                         
00003551  __floatunsisf                        
00003579  _c_int00_noargs                      
000035c7  DL_I2C_setClockConfig                
000035ed  Left_Control                         
00003611  Left_Little_Control                  
00003635  Right_Control                        
00003659  Right_Little_Control                 
0000367d  __aeabi_ui2d                         
0000367d  __floatunsidf                        
000036e1  Motor_Reset_Speed_Monitor            
00003701  delay_ms                             
00003875  DL_Timer_setCaptCompUpdateMethod     
00003891  DL_Timer_setClockConfig              
00003ad9  DL_Timer_setCaptureCompareOutCtl     
00003b39  Handle_Intersection                  
00003c9d  Key_Init_Debounce                    
00003cd5  DL_UART_setClockConfig               
00003ce7  TI_memcpy_small                      
00003cf9  __TI_decompress_none                 
00003d2d  DL_Timer_setCaptureCompareValue      
00003d3d  Key_System_Tick_Inc                  
00003d4d  __TI_zero_init                       
00003d5d  Get_Digtal_For_User                  
00003d6b  strcpy                               
00003d79  TI_memset_small                      
00003d87  SYSCFG_DL_SYSTICK_init               
00003d95  __aeabi_memclr                       
00003d95  __aeabi_memclr4                      
00003d95  __aeabi_memclr8                      
00003da1  get_systicks                         
00003dad  scheduler_init                       
00003db9  DL_Common_delayCycles                
00003dc5  __aeabi_memcpy                       
00003dc5  __aeabi_memcpy4                      
00003dc5  __aeabi_memcpy8                      
00003dcd  abort                                
00003dd3  ADC0_IRQHandler                      
00003dd3  ADC1_IRQHandler                      
00003dd3  AES_IRQHandler                       
00003dd3  CANFD0_IRQHandler                    
00003dd3  DAC0_IRQHandler                      
00003dd3  DMA_IRQHandler                       
00003dd3  Default_Handler                      
00003dd3  GROUP0_IRQHandler                    
00003dd3  HardFault_Handler                    
00003dd3  I2C0_IRQHandler                      
00003dd3  I2C1_IRQHandler                      
00003dd3  NMI_Handler                          
00003dd3  PendSV_Handler                       
00003dd3  RTC_IRQHandler                       
00003dd3  SPI0_IRQHandler                      
00003dd3  SPI1_IRQHandler                      
00003dd3  SVC_Handler                          
00003dd3  TIMA0_IRQHandler                     
00003dd3  TIMA1_IRQHandler                     
00003dd3  TIMG12_IRQHandler                    
00003dd3  TIMG6_IRQHandler                     
00003dd3  TIMG7_IRQHandler                     
00003dd3  TIMG8_IRQHandler                     
00003dd3  UART1_IRQHandler                     
00003dd3  UART2_IRQHandler                     
00003dd3  UART3_IRQHandler                     
00003dd6  C$$EXIT                              
00003dd7  HOSTexit                             
00003ddb  Reset_Handler                        
00003df1  _system_pre_init                     
00003df8  asc2_2412                            
00004b54  asc2_1608                            
00005144  asc2_1206                            
000055b8  asc2_0806                            
000058dc  __TI_Handler_Table_Base              
000058e8  __TI_Handler_Table_Limit             
000058f0  __TI_CINIT_Base                      
00005900  __TI_CINIT_Limit                     
00005900  __TI_CINIT_Warm                      
20200000  OLED_GRAM                            
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200480  gPWM_0Backup                         
2020053c  Flag_stop                            
20200540  Flag_stop1                           
20200544  Get_Encoder_countA                   
20200548  Get_Encoder_countB                   
2020054c  encoderA_cnt                         
20200550  encoderB_cnt                         
20200554  gpio_interrup1                       
20200558  gpio_interrup2                       
2020055c  task_num                             
20200560  rx_buff                              
20200660  uart_rx_buffer                       
20200718  motor_pid                            
2020073c  track_ctrl                           
2020075c  Anolog                               
2020076c  black                                
2020077c  white                                
2020078c  key1_ctrl                            
202007a0  D_Num                                
202007a8  Run                                  
202007ac  delay_times                          
202007b4  uart_rx_index                        
202007b5  uart_rx_ticks                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[243 symbols]
