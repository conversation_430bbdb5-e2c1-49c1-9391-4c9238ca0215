#ifndef _MOTOR_H
#define _MOTOR_H
#include "ti_msp_dl_config.h"
#include "bsp_system.h"

// 基础PWM控制函数
void Set_PWM(int pwmA,int pwmB);

// 传统控制函数（保持兼容性）
void Left_Control(void);
void Right_Control(void);
void Left_Large_Control(void);
void Right_Large_Control(void);
void Left_Little_Control(void);
void Right_Little_Control(void);

// 新增优化控制函数
void Motor_Smooth_Control(int error, int base_speed);
void Motor_PID_Control(int error, int base_speed);
void Motor_Square_Corner_Control(int error, int base_speed);  // 正方形轨道专用（原地转向）
void Motor_Square_Pivot_Turn(int direction, int pivot_speed); // 原地转向控制
void Motor_Stop(void);

// 速度监控和验证函数
void Motor_Speed_Monitor(int pwmA, int pwmB, const char* source);
bool Motor_Verify_Speed_Consistency(void);
void Motor_Reset_Speed_Monitor(void);

// 电机控制参数结构体
typedef struct {
    int base_speed;          // 基础速度
    int max_speed;           // 最大速度
    int min_speed;           // 最小速度
    float kp;                // 比例系数
    float ki;                // 积分系数
    float kd;                // 微分系数
    int last_error;          // 上次误差
    int integral;            // 积分累积
    int max_integral;        // 积分限幅
} Motor_PID_t;

// 全局PID参数
extern Motor_PID_t motor_pid;

// 控制参数宏定义
#define BASE_SPEED          4000    // 基础速度
#define MAX_SPEED           6000    // 最大速度
#define MIN_SPEED           1000    // 最小速度
#define MAX_TURN_DIFF       3000    // 最大转向差速

#define ABS(a)      (a>0 ? a:(-a))

#endif
