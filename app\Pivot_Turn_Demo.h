/**
 * @file Pivot_Turn_Demo.h
 * @brief 原地转向演示函数声明
 * @details 提供简单的原地转向测试函数，可在主程序中调用
 */

#ifndef _PIVOT_TURN_DEMO_H
#define _PIVOT_TURN_DEMO_H

#include "Ganway_Optimized.h"
#include "Track_Config.h"
#include "motor.h"

/**
 * @brief 简单的原地转向演示
 * @note 可以在主程序中调用来测试原地转向效果
 */
void Simple_Pivot_Demo(void);

/**
 * @brief 原地转向角度测试
 * @param direction 转向方向 (1=右转, -1=左转, 0=停止)
 * @param duration_ms 转向持续时间(毫秒)
 */
void Pivot_Turn_Angle(int direction, int duration_ms);

/**
 * @brief 检测并执行原地转向
 * @param digital_data 传感器数字数据
 * @param error 当前误差
 * @return true表示执行了原地转向
 */
bool Execute_Pivot_If_Needed(unsigned char digital_data, int error);

#endif // _PIVOT_TURN_DEMO_H
