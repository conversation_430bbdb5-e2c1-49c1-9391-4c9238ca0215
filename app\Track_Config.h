#ifndef _TRACK_CONFIG_H
#define _TRACK_CONFIG_H

/**
 * @file Track_Config.h
 * @brief 循迹系统配置文件
 * @details 包含所有可调参数，用户可根据实际情况调整
 */

/*************************** 基础速度配置 ***************************/
#define TRACK_BASE_SPEED_SLOW       3000    // 慢速模式基础速度
#define TRACK_BASE_SPEED_NORMAL     4000    // 正常模式基础速度  
#define TRACK_BASE_SPEED_FAST       5000    // 快速模式基础速度

#define TRACK_MAX_SPEED             6000    // 最大速度限制
#define TRACK_MIN_SPEED             1000    // 最小速度限制

/*************************** 转向控制配置 ***************************/
// 统一差速控制参数（基于base_speed的相对调整）
#define TRACK_TURN_DIFF             1200    // 标准转向差速
#define TRACK_TURN_DIFF_SMALL       800     // 小转向差速（微调）
#define TRACK_TURN_DIFF_LARGE       1500    // 大转向差速（急转）

// 转向控制策略配置
#define TRACK_TURN_SPEED_RATIO      0.9f    // 转向时基础速度比例
#define TRACK_MAX_TURN_DIFF         1500    // 最大转向差速（安全限制）
#define TRACK_SMOOTH_FACTOR         8       // 平滑控制系数（大幅减小）

// 速度一致性保证参数
#define TRACK_MAX_WHEEL_SPEED       TRACK_BASE_SPEED_NORMAL  // 任何轮子的最大速度
#define TRACK_SPEED_CONSISTENCY_CHECK  1    // 启用速度一致性检查

// 已废弃的固定PWM值（保留用于兼容性，但不再使用）
// 注意：新系统使用基于base_speed的动态计算，不再依赖这些固定值
#define TRACK_RIGHT_SPEED_HIGH_DEPRECATED      4500    // 已废弃
#define TRACK_RIGHT_SPEED_LOW_DEPRECATED       3000    // 已废弃
#define TRACK_LEFT_SPEED_HIGH_DEPRECATED       4500    // 已废弃
#define TRACK_LEFT_SPEED_LOW_DEPRECATED        3000    // 已废弃
#define TRACK_LITTLE_TURN_HIGH_DEPRECATED      4500    // 已废弃
#define TRACK_LITTLE_TURN_LOW_DEPRECATED       2500    // 已废弃
#define TRACK_LARGE_TURN_FORWARD_DEPRECATED    4000    // 已废弃
#define TRACK_LARGE_TURN_REVERSE_DEPRECATED    2000    // 已废弃

/*************************** PID控制配置 ***************************/
#define TRACK_PID_KP                0.8f    // 比例系数
#define TRACK_PID_KI                0.1f    // 积分系数  
#define TRACK_PID_KD                0.2f    // 微分系数
#define TRACK_PID_MAX_INTEGRAL      1000    // 积分限幅

/*************************** 传感器配置 ***************************/
#define TRACK_SENSOR_COUNT          8       // 传感器数量
#define TRACK_CENTER_POSITION       3.5f    // 中心位置
#define TRACK_WEIGHT_FACTOR         100     // 位置权重系数

/*************************** 状态检测配置 ***************************/
#define TRACK_LOST_COUNT_THRESHOLD  5       // 丢线计数阈值
#define TRACK_INTERSECTION_SENSORS  6       // 十字路口传感器数量阈值
#define TRACK_SHARP_TURN_SENSORS    3       // 急转弯传感器数量

/*************************** 自适应控制配置 ***************************/
#define TRACK_ADAPTIVE_SPEED_RATIO  0.7f    // 急转弯时速度比例
#define TRACK_INTERSECTION_DELAY    100     // 路口通过延时(ms)

/*************************** 调试配置 ***************************/
#define TRACK_DEBUG_ENABLE          1       // 启用调试功能
#define TRACK_DEBUG_UART_ENABLE     0       // 启用串口调试输出
#define TRACK_DEBUG_OLED_ENABLE     1       // 启用OLED调试显示

/*************************** 模式选择配置 ***************************/
// 默认循迹模式（可在运行时切换）
#define TRACK_DEFAULT_MODE          TRACK_MODE_WEIGHTED

// 速度模式选择
typedef enum {
    SPEED_MODE_SLOW = 0,
    SPEED_MODE_NORMAL,
    SPEED_MODE_FAST
} Speed_Mode_t;

#define TRACK_DEFAULT_SPEED_MODE    SPEED_MODE_NORMAL

/*************************** 安全保护配置 ***************************/
#define TRACK_EMERGENCY_STOP_ENABLE 1       // 启用紧急停止功能
#define TRACK_LOST_LINE_TIMEOUT     1000    // 丢线超时时间(ms)
#define TRACK_MAX_ERROR_COUNT       10      // 最大错误计数

/*************************** 校准配置 ***************************/
#define TRACK_AUTO_CALIBRATE_ENABLE 1       // 启用自动校准
#define TRACK_CALIBRATE_SAMPLES     50      // 校准采样次数
#define TRACK_CALIBRATE_DELAY       10      // 校准延时(ms)

/*************************** 性能优化配置 ***************************/
#define TRACK_USE_FLOAT_CALC        1       // 使用浮点计算（精度更高）
#define TRACK_USE_LOOKUP_TABLE      0       // 使用查找表（速度更快）
#define TRACK_FILTER_ENABLE         1       // 启用数字滤波

// 滤波参数
#define TRACK_FILTER_ALPHA          0.8f    // 低通滤波系数

/*************************** 用户自定义区域 ***************************/
// 用户可以在这里添加自定义的配置参数

// 示例：不同赛道的预设参数
#ifdef TRACK_TYPE_STANDARD
    #undef TRACK_BASE_SPEED_NORMAL
    #define TRACK_BASE_SPEED_NORMAL     4000
    #undef TRACK_PID_KP
    #define TRACK_PID_KP                0.8f
#endif

#ifdef TRACK_TYPE_COMPLEX
    #undef TRACK_BASE_SPEED_NORMAL  
    #define TRACK_BASE_SPEED_NORMAL     3500
    #undef TRACK_PID_KP
    #define TRACK_PID_KP                1.0f
#endif

#ifdef TRACK_TYPE_HIGH_SPEED
    #undef TRACK_BASE_SPEED_NORMAL
    #define TRACK_BASE_SPEED_NORMAL     5000
    #undef TRACK_MAX_TURN_DIFF
    #define TRACK_MAX_TURN_DIFF         2500
#endif

/*************************** 参数验证 ***************************/
// 编译时参数检查
#if TRACK_BASE_SPEED_NORMAL > TRACK_MAX_SPEED
    #error "Base speed cannot exceed max speed"
#endif

#if TRACK_BASE_SPEED_NORMAL < TRACK_MIN_SPEED
    #error "Base speed cannot be less than min speed"
#endif

#if TRACK_SENSOR_COUNT != 8
    #warning "Sensor count is not 8, please check compatibility"
#endif

// 速度一致性验证
#if TRACK_TURN_DIFF > TRACK_BASE_SPEED_NORMAL
    #error "Turn diff cannot exceed base speed - this would cause negative wheel speeds"
#endif

#if TRACK_TURN_DIFF_LARGE > TRACK_BASE_SPEED_NORMAL
    #error "Large turn diff cannot exceed base speed"
#endif

#if TRACK_MAX_TURN_DIFF > TRACK_BASE_SPEED_NORMAL
    #error "Max turn diff cannot exceed base speed"
#endif

// 差速参数合理性检查
#if TRACK_TURN_DIFF_SMALL >= TRACK_TURN_DIFF
    #error "Small turn diff should be less than standard turn diff"
#endif

#if TRACK_TURN_DIFF >= TRACK_TURN_DIFF_LARGE
    #error "Standard turn diff should be less than large turn diff"
#endif

#endif /* _TRACK_CONFIG_H */
